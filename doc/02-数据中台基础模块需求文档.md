# 02-数据中台基础模块需求文档

## 模块概述
数据中台基础模块是整个数据中台的核心底座，提供数据采集、存储、处理、治理、安全等基础能力，支撑上层业务应用的数据需求。

## 整体业务流程图

```mermaid
graph TB
    A[数据源] --> B[数据汇聚管理]
    B --> C[数据标准管理]
    C --> D[数据综合管理]
    D --> E[数据质量管理]
    E --> F[数据仓库管理]
    F --> G[数据资源目录管理]
    G --> H[数据安全管理]
    H --> I[数据报送管理]
    I --> J[数据运维管理]
    J --> K[数据共享交换]
    K --> L[系统管理]
    
    M[业务系统] --> B
    N[外部系统] --> B
    
    K --> O[业务应用]
    K --> P[国资监管平台]
    
    L --> B
    L --> C
    L --> D
    L --> E
    L --> F
    L --> G
    L --> H
    L --> I
    L --> J
    L --> K
```

## 1. 数据汇聚管理模块

### 功能需求
- **多源数据接入**: 支持结构化、半结构化和非结构化数据类型
- **增量数据接入**: 通过定制计划任务实现按周期增量数据抽取
- **实时数据采集**: 基于CDC及消息通道等多种实时采集模式
- **数据集成组件**: 支持全B/S可视化拖拽式流程设计
- **数据同步**: 采用可视化图形组件拖拽方式快速开发同步作业

### 业务流程图
```mermaid
graph LR
    A[数据源识别] --> B[连接配置]
    B --> C[数据抽取]
    C --> D[数据转换]
    D --> E[数据加载]
    E --> F[数据验证]
    F --> G[数据入库]
    
    H[实时数据流] --> I[CDC捕获]
    I --> J[消息队列]
    J --> K[实时处理]
    K --> G
```

### 技术要求
- 支持主流关系型RDBMS、MPP、Excel/CSV文本数据源
- 支持MySQL、SQL Server、达梦等数据库日志实时同步
- 提供高效的离线数据抽取、加载服务

## 2. 数据标准管理模块

### 功能需求
- **数据元管理**: 支持多级分类管理，包括国家标准、地方标准、行业标准
- **码表管理**: 支持码表的标准化管理和版本控制
- **编码规则管理**: 支持编码规则的统一管理和应用
- **技术标准管理**: 支持主流数据库建模的字段类型映射
- **数据字典管理**: 描述和定义数据元素的类型、格式、标识符

### 业务流程图
```mermaid
graph TD
    A[标准制定] --> B[标准审核]
    B --> C[标准发布]
    C --> D[标准应用]
    D --> E[标准维护]
    E --> F[版本管理]
    
    G[数据元定义] --> H[码表配置]
    H --> I[编码规则设置]
    I --> J[技术标准映射]
    J --> K[数据字典生成]
```

### 验收标准
- 建立完整的数据标准体系
- 实现数据标准的版本化管理
- 支持标准的自动化应用和校验

## 3. 数据综合管理模块

### 功能需求
- **消息队列管理**: 支持消息队列数据源信息管理
- **FTP数据源管理**: 支持FTP数据源的连接和管理
- **文件管理**: 支持多种文件类型的上传、预览、管理
- **主数据管理**: 提供主数据模型建立、维护及配置管理
- **元数据管理**: 管理技术元数据、业务元数据和操作元数据
- **数据加工管理**: 提供数据清洗、转换、汇总等加工能力

### 业务流程图
```mermaid
graph TB
    A[数据源注册] --> B[元数据采集]
    B --> C[主数据识别]
    C --> D[数据加工规则定义]
    D --> E[数据清洗]
    E --> F[数据转换]
    F --> G[数据汇总]
    G --> H[数据加载]
    H --> I[数据验证]
```

## 4. 数据质量管理模块

### 功能需求
- **质检任务管理**: 基于质量检查规则构建校验任务
- **质检规则配置**: 支持表级质检规则配置，包括完整性、准确性检查
- **质检结果分析**: 提供质检结果的统计分析和趋势分析

### 业务流程图
```mermaid
graph LR
    A[质检规则定义] --> B[质检任务创建]
    B --> C[质检执行]
    C --> D[结果分析]
    D --> E[问题识别]
    E --> F[问题处理]
    F --> G[质量报告]
```

## 5. 数据仓库管理模块

### 功能需求
- **数仓分层**: 支持基于数据连接构建多种数仓
- **数仓分域**: 提供采集域、主题域、应用域的多级管理
- **数据建模**: 支持可视化、DDL、逆向等建表模式

### 业务流程图
```mermaid
graph TD
    A[需求分析] --> B[概念建模]
    B --> C[逻辑建模]
    C --> D[物理建模]
    D --> E[模型实施]
    E --> F[模型优化]
    
    G[数仓分层设计] --> H[分域规划]
    H --> I[主题建模]
    I --> J[维度建模]
    J --> K[事实表设计]
```

## 6. 数据资源目录管理模块

### 功能需求
数据资源目录是实现信息资源共享、业务协同和数据开放的基础，是各部门之间信息共享及数据向社会开放的依据。

- **资源目录管理**: 支持对数据资源目录进行管理，包括新增、编辑、删除、回收、发布等
- **资源编制能力**: 提供各类数据资源的编制能力，包括数据表、API、文件、消息队列等
- **资源发布管理**: 支持对数据资源进行发布、下线、编辑、删除、版本对比等管理
- **多目录发布**: 支持同时发布多个资源目录

### 业务流程图
```mermaid
graph LR
    A[资源调研] --> B[资源编目]
    B --> C[资源分类]
    C --> D[权限设置]
    D --> E[资源发布]
    E --> F[版本管理]
    F --> G[资源维护]
```

## 7. 数据安全管理模块

### 功能需求
数据安全依托数据分类分级、加密脱密策略、SSL/TLS传输加密协议，建立数据存储保护、访问控制等机制。

#### 敏感类型管理
- 支持对敏感分类按照不同业务类别归纳管理
- 支持敏感类型的新增、编辑、启用、停用和删除等操作
- 支持按照内容、字段英文名称、字段注释和敏感词同义词识别字段的敏感分类
- 支持精确、模糊和正则表达式等匹配策略识别字段的敏感分类

#### 脱敏规则管理
- 支持对脱敏规则的新增、编辑、启用、停用和删除等操作
- 支持替换、掩码、重排、加密、小数泛化、区间泛化、时间取整和数据仿真等脱敏策略
- 支持输入样本数据测试脱敏规则的执行结果
- 支持编排脱敏加密规则于加工作业，加密后存储至目标端

#### 敏感识别
- 支持基于主流、国产数据库的识别任务的新增、编辑、开启、关闭、删除和实例查看
- 支持手动、定时和周期性执行识别任务
- 支持配置识别任务按全表或抽样扫描
- 支持手动修正敏感类型和安全等级的识别结果

### 业务流程图
```mermaid
graph TD
    A[数据分类分级] --> B[敏感数据识别]
    B --> C[脱敏规则配置]
    C --> D[数据加密处理]
    D --> E[访问权限控制]
    E --> F[安全审计]
    F --> G[合规检查]
```

## 8. 数据报送管理模块

### 功能需求
数据报送主要面向集团管控或国资监管要求进行数据报送管理。

#### 机构管理
- 主要实现对报送机构的统一管理
- 支持机构组管理和机构管理，包含对机构组和机构的新增、编辑和删除功能

#### 指标管理
- 主要实现对报送数据指标的统一管理
- 根据业务主题按指标类别、指标标准建立指标库
- 支持对指标详情进行新建、查看、编辑、删除功能

#### 规则管理
- 主要实现对报送数据规则的统一管理
- 根据数据效验的需求按指标类别、指标标准建立指标库
- 支持对指标详情进行新建、查看、编辑、删除、停用/启用功能

#### 报表模版管理
- 支持对报送数据模版进行统一管理
- 根据业务管理的需求建立报表模块库
- 支持新建报表、编辑、查看、删除、停用/启用功能

#### 报送任务管理
- 根据报送数据实际需求，进行报送任务管理
- 包含业务明细表管理、报表实例管理和核验管理
- 支持对业务明细表的创建、编辑、查看、停用/启用等功能

#### 企业报送管理
- 建立下属企业报表报送管理的渠道
- 支持在线填报和excel表上传
- 支持按报送类别和报送时间进行查看报表详情和报表状态
- 支持根据校验提示重新编辑报表数据，重新上报

#### 报表台账管理
- 根据报送数据任务情况，支持按企业、报表类型查询全部报表
- 包含报表状态（未上报、已上报、校验失败、延期上报）
- 支持按企业、报送时段、报表类型进行汇总，支持汇总表查询、导出功能

### 业务流程图
```mermaid
graph LR
    A[报送需求] --> B[指标配置]
    B --> C[模版制作]
    C --> D[数据填报]
    D --> E[数据校验]
    E --> F[数据上报]
    F --> G[结果确认]
    G --> H[台账管理]
```

## 9. 数据运维管理模块

### 功能需求
数据运维管理是指通过采集数据接入、处理、组织和服务等各项任务的状态信息，对异常状态进行告警和处置。

#### 任务调度
- 支持配置依赖的方式将任务按照串联、并行、依赖等关系关联起来
- 可实时、可视化监控任务的运行状态
- 支持工作流定时调度、依赖调度、手动调度、停止/恢复
- 支持失败重试/告警、从指定节点恢复失败、Kill节点操作
- 支持工作流全局参数及节点自定义参数设置

#### 任务监控
- 任务监控主要监控任务的运行情况，包含任务模式、实例模式
- 支持血缘查看、重跑、恢复失败、置成功、停止、删除、运行日志功能
- 实时查看任意节点的运行日志

#### 告警管理
- 告警管理管理告警组信息，配置关联人员
- 任务执行失败、任务执行成功则会通过邮件服务发送邮件到指定人员
- 支持自定义组合资源告警规则以实现对关键节点服务的运维监控

#### 资源监控
- 资源监控是指采集数据治理平台关联硬件服务器资源状态、数据库连接状态
- 在平台上实时展现

### 业务流程图
```mermaid
graph TB
    A[任务创建] --> B[任务调度]
    B --> C[任务执行]
    C --> D[状态监控]
    D --> E{执行状态}
    E -->|成功| F[任务完成]
    E -->|失败| G[告警通知]
    G --> H[故障处理]
    H --> I[任务重跑]
    I --> C
```

## 10. 数据共享交换模块

### 功能需求
采用数据API服务对外提供数据。通过配置化、低代码能力，快速将数据封装为API服务。

#### 共享API池管理
- 实现对共享服务接口API管理，可对当前可共享的API进行列表展示
- 包括HTTP，FTP服务
- 实现具体的服务信息展示，包括服务编码，服务名称，接口地址，集群节点和端口，请求类型，有效性等
- 支持HTTP服务注册、高级注册、服务查询、服务修改、服务删除、服务启停

#### 服务列表管理
- 服务列表可对已经创建好的服务以目录的形式查看
- 可对服务详情进行查看，也可以对服务进行上下架操作
- 支持模糊搜索、服务查询、详情查看、上下架操作、服务测试

#### 密钥管理
- 用户可自行在账户"我的密钥"侧申请密钥，审核通过后可进行使用
- 平台支持管理员账号对密钥的统一管理，提供查询密钥功能
- 支持管理员账号对密钥进行生效/时效管理

#### 服务审批
- 提供数据下载的权限审批功能
- 我的服务处，可以查看到我申请的所有服务
- 创建好服务，需要进行审批之后才可以进行后续使用
- 实现与OA进行集成

### 业务流程图
```mermaid
graph LR
    A[服务注册] --> B[服务配置]
    B --> C[服务发布]
    C --> D[权限申请]
    D --> E[服务审批]
    E --> F[密钥分配]
    F --> G[服务调用]
    G --> H[使用监控]
```

## 11. 系统管理模块

### 功能需求
为各级管理工作人员提供机构管理、用户管理、角色管理、数据使用管理等功能。

#### 机构管理
- 通过系统化的方式对组织结构进行建立、维护和更新
- 提供多层级组织架构管理功能，以树型结构的形式展现
- 支持对各级机构信息进行新增、编辑、删除等操作

#### 用户管理
- 系统管理员可根据权限对本级与下级机构用户进行管理
- 包括系统用户新增、系统用户角色分配、系统用户密码重置和系统用户账号的禁用与删除等

#### 角色管理
- 通过角色管理功能，可对系统访问进行统一的控制
- 根据用户的级别，赋予用户相应的功能操作权限和数据权限
- 面向系统管理员提供维护系统角色功能，为用户分配对应的角色

#### 权限管理
- 针对平台用户的权限进行管理
- 可对菜单的权限、按钮的权限、接口的权限进行设置

#### 日志管理
- 对用户的操作痕迹记录，包括功能模块、操作用户、操作类型、操作时间、访问IP等
- 对平台产生的告警日志进行查看和监控，包括告警业务类型、告警时间、告警内容、告警状态等

### 业务流程图
```mermaid
graph TD
    A[用户注册] --> B[角色分配]
    B --> C[权限设置]
    C --> D[系统访问]
    D --> E[操作记录]
    E --> F[日志审计]

    G[机构管理] --> H[用户管理]
    H --> I[角色管理]
    I --> J[权限管理]
```

## 技术架构要求
- 支持主流、国产数据库
- 提供低代码开发环境
- 支持分布式部署和集群管理
- 提供完整的API接口
- 支持微服务架构
- 提供统一的身份认证和授权机制

## 验收标准
1. 所有基础模块功能完整实现
2. 数据处理性能满足要求（支持50并发用户，响应时间<5秒）
3. 数据质量达到预期标准（数据质量合格率≥95%）
4. 系统稳定性和可靠性验证通过（系统可用性≥99.5%）
5. 安全性测试通过（通过等保测评）
6. 数据采集覆盖率达到100%
7. 国资数据报送及时率达到100%

---
*本文档详细描述了数据中台基础模块的功能需求和技术要求，为项目实施提供指导。*
